import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:police/common/model/analyze_details_model/alarms_statistic.dart';
import 'package:police/common/model/analyze_details_model/sort.dart';
import 'package:police/common/model/analyze_details_model/village_list.dart';
import 'package:police/common/model/analyze_model.dart';
import 'package:police/common/widget/corner_card.dart';
import 'package:police/common/widget/pie_chart.dart';
import 'package:police/page/analyze/controller.dart';
import 'package:police/page/analyzeDetail/index.dart';
import 'package:community_charts_flutter/community_charts_flutter.dart'
    as charts;

class AnalyzeTable extends StatelessWidget {
  const AnalyzeTable(
      {super.key, required this.controller, required this.detail});

  final AnalyzeModel detail;
  final AnalyzedetailController controller;

  @override
  Widget build(BuildContext context) {
    List<AlarmsStatistic> datas = controller.details.alarmsStatistics!;
    return Column(
      children: List.generate(datas.length + 1, (index) {
        List<Sort> sort = [];
        List<VillageList> villages = [];

        /// 警情总数
        int total = 0;

        int j = index;
        if (index > 0) {
          j = index - 1;
          sort = datas[j].sort!;
        } else {
          villages = controller.details.villageList!;
        }

        for (var element in villages) {
          total += element.alarmCount ?? 0;
        }

        return Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 50),
          child: CornerCard(
            child: index > 0
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        '${_getYearMonth(detail)}${datas[j].name}警情统计表',
                        style:
                            const TextStyle(fontSize: 25, color: Colors.white),
                      ),
                      const SizedBox(height: 10),
                      Wrap(
                        spacing: 10,
                        runSpacing: 10,
                        children: List.generate(sort.length + 1, (i) {
                          return Container(
                            width: 100,
                            alignment: Alignment.center,
                            child: Column(
                              children: [
                                Text(
                                  i == 0 ? '合计（起）' : "${sort[i - 1].name}（起）",
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(color: Colors.white),
                                ),
                                Text(
                                  i == 0
                                      ? datas[j].alarmCount.toString()
                                      : sort[i - 1].count.toString(),
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          );
                        }),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        '${_getYearMonth(detail)}警情发案区域统计表',
                        style:
                            const TextStyle(fontSize: 25, color: Colors.white),
                      ),
                      if (total > 0)
                        Column(
                          children: [
                            const SizedBox(height: 10),
                            Wrap(
                              spacing: 10,
                              runSpacing: 10,
                              children: List.generate(villages.length, (i) {
                                return Column(
                                  children: [
                                    Text(
                                      villages[i].name ?? '',
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                    Text(
                                      villages[i].alarmCount.toString(),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ],
                                );
                              }),
                            ),
                            const SizedBox(height: 10),
                            PieChart(
                              [
                                charts.Series(
                                  id: 'id',
                                  data: villages,
                                  domainFn: (data, _) => data.name ?? '',
                                  measureFn: (data, _) => data.alarmCount,
                                  labelAccessorFn: (datum, index) =>
                                      _getPercent(
                                          villages, datum.alarmCount ?? 0),
                                  colorFn: (datum, index) => charts.Color.fromHex(
                                      code:
                                          '#${Random().nextInt(0xffffff).toRadixString(16).padLeft(6, '0')}'),
                                )
                              ],
                              showBehavior: true,
                            ),
                          ],
                        )
                    ],
                  ),
          ),
        );
      }),
    );
  }

  /// 获取年月
  String _getYearMonth(AnalyzeModel detail) {
    String str = '';

    switch (detail.types) {
      case AnalyzeSortTypes.month:
        str = '${detail.yearMonthDay!.split('-')[1]}月份';

        break;
      case AnalyzeSortTypes.quarter:
        str =
            '(${detail.yearMonthDay!.split('-')[0]})第${controller.getQuarter(detail)}季度';

        break;
      case AnalyzeSortTypes.year:
        str = '${detail.yearMonthDay!.split('-')[0]}年';

        break;
      default:
    }

    return str;
  }
}
